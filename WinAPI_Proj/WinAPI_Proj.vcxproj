<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{e343f2e3-9c35-45a5-b4d2-36e197140881}</ProjectGuid>
    <RootNamespace>WinAPIProj</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup>
    <EnableUnitySupport>true</EnableUnitySupport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)Output\bin_debug\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)Output\bin\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>C:\WinAPIProject\Output\bin_debug\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>C:\WinAPIProject\Output\bin\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <MinFilesInUnityFile>8</MinFilesInUnityFile>
      <UnityFilesDirectory>$(ProjectDir)\Unity\</UnityFilesDirectory>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <MinFilesInUnityFile>8</MinFilesInUnityFile>
      <UnityFilesDirectory>$(ProjectDir)\Unity\</UnityFilesDirectory>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <MinFilesInUnityFile>8</MinFilesInUnityFile>
      <UnityFilesDirectory>$(ProjectDir)\Unity\</UnityFilesDirectory>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>$(ProjectDir)Source\Object\UI\ChatUI;$(ProjectDir);$(ProjectDir)Source;$(ProjectDir)Source\Component;$(ProjectDir)Source\Core;$(ProjectDir)Source\Header;$(ProjectDir)Source\Manager;$(ProjectDir)Source\Module;$(ProjectDir)Source\Object;$(ProjectDir)Source\Prefab;$(ProjectDir)Source\Resource;$(ProjectDir)Source\Scene;$(ProjectDir)Source\Tool;$(ProjectDir)Source\Component\Animator;$(ProjectDir)Source\Component\Collider;$(ProjectDir)Source\Component\Gravity;$(ProjectDir)Source\Component\RigidBody;$(ProjectDir)Source\Component\Animator\Animation;$(ProjectDir)Source\Manager\BrushMgr;$(ProjectDir)Source\Manager\Camera;$(ProjectDir)Source\Manager\CollisionMgr;$(ProjectDir)Source\Manager\CSoundMgr;$(ProjectDir)Source\Manager\EventMgr;$(ProjectDir)Source\Manager\KeyMgr;$(ProjectDir)Source\Manager\PathMgr;$(ProjectDir)Source\Manager\ResMgr;$(ProjectDir)Source\Manager\SceneMgr;$(ProjectDir)Source\Manager\TimeMgr;$(ProjectDir)Source\Manager\UIMgr;$(ProjectDir)Source\Module\AI;$(ProjectDir)Source\Module\AI\State;$(ProjectDir)Source\Module\AI\State\Idle;$(ProjectDir)Source\Module\AI\State\Trace;$(ProjectDir)Source\Object\Ground;$(ProjectDir)Source\Object\Tile;$(ProjectDir)Source\Object\UI;$(ProjectDir)Source\Object\UI\BtnUI;$(ProjectDir)Source\Object\UI\PanelUI;$(ProjectDir)Source\Resource\Sound;$(ProjectDir)Source\Resource\Texture;$(ProjectDir)Source\Scene\Scene_Stage_01;$(ProjectDir)Source\Scene\Scene_Title;$(ProjectDir)Source\Scene\Scene_Tool</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <MinFilesInUnityFile>8</MinFilesInUnityFile>
      <UnityFilesDirectory>$(ProjectDir)\Unity\</UnityFilesDirectory>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="Source\Manager\CObjectPool.h" />
    <ClInclude Include="Source\Module\AI\State\Dead\CDeadState.h" />
    <ClInclude Include="Source\Module\AI\State\Shooter\CAimingState.h" />
    <ClInclude Include="Source\Module\AI\State\Spawning\CSpawningState.h" />
    <ClInclude Include="Source\Module\AI\State\Subdued\CSubduedState.h" />
    <ClInclude Include="Source\Object\CBullet.h" />
    <ClInclude Include="Source\Object\Ground\CWall.h" />
    <ClInclude Include="Source\Object\Monster\CShooterHead.h" />
    <ClInclude Include="Source\Object\Monster\CShooterMonster.h" />
    <ClInclude Include="Source\Object\Trigger\CTrigger.h" />
    <ClInclude Include="Source\Object\UI\ChatUI\CTextUI.h" />
    <ClInclude Include="Source\CHook.h" />
    <ClInclude Include="Source\Component\Animator\Animation\CAnimation.h" />
    <ClInclude Include="Source\Component\Animator\CAnimator.h" />
    <ClInclude Include="Source\Component\Collider\CCollider.h" />
    <ClInclude Include="Source\Component\Gravity\CGravity.h" />
    <ClInclude Include="Source\Component\RigidBody\CRigidBody.h" />
    <ClInclude Include="Source\Core\CCore.h" />
    <ClInclude Include="Source\Manager\BrushMgr\CBrushManager.h" />
    <ClInclude Include="Source\framework.h" />
    <ClInclude Include="Source\Header\define.h" />
    <ClInclude Include="Source\Header\func.h" />
    <ClInclude Include="Source\Header\global.h" />
    <ClInclude Include="Source\Header\pch.h" />
    <ClInclude Include="Source\Header\struct.h" />
    <ClInclude Include="Source\Manager\Camera\CCamera.h" />
    <ClInclude Include="Source\Manager\CollisionMgr\CCollisionMgr.h" />
    <ClInclude Include="Source\Manager\CSoundMgr\CSoundMgr.h" />
    <ClInclude Include="Source\Manager\EventMgr\CEventMgr.h" />
    <ClInclude Include="Source\Manager\KeyMgr\CKeyMgr.h" />
    <ClInclude Include="Source\Manager\PathMgr\CPathMgr.h" />
    <ClInclude Include="Source\Manager\ResMgr\CResMgr.h" />
    <ClInclude Include="Source\Manager\SceneMgr\CSceneMgr.h" />
    <ClInclude Include="Source\Manager\TimeMgr\CTimeMgr.h" />
    <ClInclude Include="Source\Manager\UIMgr\CUIMgr.h" />
    <ClInclude Include="Source\Module\AI\AI.h" />
    <ClInclude Include="Source\Module\AI\State\CState.h" />
    <ClInclude Include="Source\Module\AI\State\Idle\CIdleState.h" />
    <ClInclude Include="Source\Module\AI\State\Trace\CTraceState.h" />
    <ClInclude Include="Source\Object\CMonster.h" />
    <ClInclude Include="Source\Object\GameObject.h" />
    <ClInclude Include="Source\Object\Ground\CGround.h" />
    <ClInclude Include="Source\Object\Ground\CNormalGround.h" />
    <ClInclude Include="Source\Object\Tile\CBackGround.h" />
    <ClInclude Include="Source\Object\Tile\CTile.h" />
    <ClInclude Include="Source\Object\UI\BtnUI\CBtnUI.h" />
    <ClInclude Include="Source\Object\UI\CUI.h" />
    <ClInclude Include="Source\Object\UI\DamageEffect\CDamageEffectUI.h" />
    <ClInclude Include="Source\Object\UI\PanelUI\CPanelUI.h" />
    <ClInclude Include="Source\Prefab\CGroundPrefab.h" />
    <ClInclude Include="Source\Prefab\CMonPrefab.h" />
    <ClInclude Include="Source\resource.h" />
    <ClInclude Include="Source\Resource\CRes.h" />
    <ClInclude Include="Source\Resource\Sound\CSound.h" />
    <ClInclude Include="Source\Resource\Texture\CTexture.h" />
    <ClInclude Include="Source\Scene\CScene.h" />
    <ClInclude Include="Source\Scene\Scene_Stage_01\Scene_Stage_01.h" />
    <ClInclude Include="Source\Scene\Scene_Title\CScene_Title.h" />
    <ClInclude Include="Source\Scene\Scene_Tool\CScene_Tool.h" />
    <ClInclude Include="Source\targetver.h" />
    <ClInclude Include="Source\Tool\CTool.h" />
    <ClInclude Include="Source\Tool\Vec2.h" />
    <ClInclude Include="Source\MouseCursor.h" />
    <ClInclude Include="Source\PlayerArm.h" />
    <ClInclude Include="Source\Raycast.h" />
    <ClInclude Include="Source\SPlayer.h" />
    <ClInclude Include="Source\WinAPI_Proj.h" />
    <ClInclude Include="framework.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="Source\Manager\CObjectPool.cpp">
      <RuntimeLibrary>MultiThreadedDebugDll</RuntimeLibrary>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <Optimization>Disabled</Optimization>
      <SupportJustMyCode>true</SupportJustMyCode>
      <AssemblerOutput>NoListing</AssemblerOutput>
      <AssemblerListingLocation>x64\Debug\</AssemblerListingLocation>
      <UndefineAllPreprocessorDefinitions>false</UndefineAllPreprocessorDefinitions>
      <BrowseInformation>false</BrowseInformation>
      <BrowseInformationFile>x64\Debug\</BrowseInformationFile>
      <CompileAs>Default</CompileAs>
      <ConformanceMode>true</ConformanceMode>
      <DiagnosticsFormat>Column</DiagnosticsFormat>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <ErrorReporting>Prompt</ErrorReporting>
      <ExpandAttributedSource>false</ExpandAttributedSource>
      <ExceptionHandling>Sync</ExceptionHandling>
      <EnableASAN>false</EnableASAN>
      <EnableFuzzer>false</EnableFuzzer>
      <EnableFiberSafeOptimizations>false</EnableFiberSafeOptimizations>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <EnableVectorLength>NotSet</EnableVectorLength>
      <FloatingPointModel>Precise</FloatingPointModel>
      <ForceConformanceInForLoopScope>true</ForceConformanceInForLoopScope>
      <GenerateModuleDependencies>false</GenerateModuleDependencies>
      <GenerateSourceDependencies>false</GenerateSourceDependencies>
      <GenerateXMLDocumentationFiles>false</GenerateXMLDocumentationFiles>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <IgnoreStandardIncludePath>false</IgnoreStandardIncludePath>
      <LanguageStandard>Default</LanguageStandard>
      <LanguageStandard_C>Default</LanguageStandard_C>
      <MinimalRebuild>false</MinimalRebuild>
      <ModuleDependenciesFile>x64\Debug\</ModuleDependenciesFile>
      <ModuleOutputFile>x64\Debug\</ModuleOutputFile>
      <OmitDefaultLibName>false</OmitDefaultLibName>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <ObjectFileName>x64\Debug\</ObjectFileName>
      <CallingConvention>Cdecl</CallingConvention>
      <ProgramDataBaseFileName>x64\Debug\vc143.pdb</ProgramDataBaseFileName>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>x64\Debug\WinAPI_Proj.pch</PrecompiledHeaderOutputFile>
      <PreprocessToFile>false</PreprocessToFile>
      <PreprocessKeepComments>false</PreprocessKeepComments>
      <PreprocessSuppressLineNumbers>false</PreprocessSuppressLineNumbers>
      <RemoveUnreferencedCodeData>true</RemoveUnreferencedCodeData>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
      <ShowIncludes>false</ShowIncludes>
      <SourceDependenciesFile>x64\Debug\</SourceDependenciesFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <SmallerTypeCheck>false</SmallerTypeCheck>
      <SpectreMitigation>false</SpectreMitigation>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <TrackerLogDirectory>x64\Debug\WinAPI_Proj.tlog\</TrackerLogDirectory>
      <TranslateIncludes>false</TranslateIncludes>
      <MinimalRebuildFromTracking>true</MinimalRebuildFromTracking>
      <TreatWarningAsError>false</TreatWarningAsError>
      <TreatWChar_tAsBuiltInType>true</TreatWChar_tAsBuiltInType>
      <UseFullPaths>true</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <XMLDocumentationFileName>x64\Debug\</XMLDocumentationFileName>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <IntelJCCErratum>false</IntelJCCErratum>
      <BuildStlModules>false</BuildStlModules>
      <TreatAngleIncludeAsExternal>false</TreatAngleIncludeAsExternal>
      <ExternalWarningLevel>InheritWarningLevel</ExternalWarningLevel>
      <TreatExternalTemplatesAsInternal>true</TreatExternalTemplatesAsInternal>
      <DisableAnalyzeExternal>false</DisableAnalyzeExternal>
      <IncludeInUnityFile>true</IncludeInUnityFile>
      <UnityFilesDirectory>C:\WinAPIProject\WinAPI_Proj\\Unity\</UnityFilesDirectory>
      <OrderInUnityFile>100</OrderInUnityFile>
      <CombineFilesOnlyFromTheSameFolder>false</CombineFilesOnlyFromTheSameFolder>
      <MinFilesInUnityFile>8</MinFilesInUnityFile>
      <MaxFilesInUnityFile>0</MaxFilesInUnityFile>
      <MinUnityFiles>1</MinUnityFiles>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;_CRT_SECURE_NO_WARNINGS;_UNICODE;UNICODE;</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <AdditionalIncludeDirectories>C:\WinAPIProject\WinAPI_Proj\Source\Object\UI\ChatUI;C:\WinAPIProject\WinAPI_Proj\;C:\WinAPIProject\WinAPI_Proj\Source;C:\WinAPIProject\WinAPI_Proj\Source\Component;C:\WinAPIProject\WinAPI_Proj\Source\Core;C:\WinAPIProject\WinAPI_Proj\Source\Header;C:\WinAPIProject\WinAPI_Proj\Source\Manager;C:\WinAPIProject\WinAPI_Proj\Source\Module;C:\WinAPIProject\WinAPI_Proj\Source\Object;C:\WinAPIProject\WinAPI_Proj\Source\Prefab;C:\WinAPIProject\WinAPI_Proj\Source\Resource;C:\WinAPIProject\WinAPI_Proj\Source\Scene;C:\WinAPIProject\WinAPI_Proj\Source\Tool;C:\WinAPIProject\WinAPI_Proj\Source\Component\Animator;C:\WinAPIProject\WinAPI_Proj\Source\Component\Collider;C:\WinAPIProject\WinAPI_Proj\Source\Component\Gravity;C:\WinAPIProject\WinAPI_Proj\Source\Component\RigidBody;C:\WinAPIProject\WinAPI_Proj\Source\Component\Animator\Animation;C:\WinAPIProject\WinAPI_Proj\Source\Manager\Camera;C:\WinAPIProject\WinAPI_Proj\Source\Manager\CollisionMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\CSoundMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\EventMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\KeyMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\PathMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\ResMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\SceneMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\TimeMgr;C:\WinAPIProject\WinAPI_Proj\Source\Manager\UIMgr;C:\WinAPIProject\WinAPI_Proj\Source\Module\AI;C:\WinAPIProject\WinAPI_Proj\Source\Module\AI\State;C:\WinAPIProject\WinAPI_Proj\Source\Module\AI\State\Idle;C:\WinAPIProject\WinAPI_Proj\Source\Module\AI\State\Trace;C:\WinAPIProject\WinAPI_Proj\Source\Object\Ground;C:\WinAPIProject\WinAPI_Proj\Source\Object\Tile;C:\WinAPIProject\WinAPI_Proj\Source\Object\UI;C:\WinAPIProject\WinAPI_Proj\Source\Object\UI\BtnUI;C:\WinAPIProject\WinAPI_Proj\Source\Object\UI\PanelUI;C:\WinAPIProject\WinAPI_Proj\Source\Resource\Sound;C:\WinAPIProject\WinAPI_Proj\Source\Resource\Texture;C:\WinAPIProject\WinAPI_Proj\Source\Scene\Scene_Stage_01;C:\WinAPIProject\WinAPI_Proj\Source\Scene\Scene_Title;C:\WinAPIProject\WinAPI_Proj\Source\Scene\Scene_Tool</AdditionalIncludeDirectories>
      <LinkCompiled>true</LinkCompiled>
    </ClCompile>
    <ClCompile Include="Source\Module\AI\State\Dead\CDeadState.cpp" />
    <ClCompile Include="Source\Module\AI\State\Shooter\CAimingState.cpp" />
    <ClCompile Include="Source\Module\AI\State\Spawning\CSpawningState.cpp" />
    <ClCompile Include="Source\Module\AI\State\Subdued\CSubduedState.cpp" />
    <ClCompile Include="Source\Object\CBullet.cpp" />
    <ClCompile Include="Source\Object\Ground\CWall.cpp" />
    <ClCompile Include="Source\Object\Monster\CShooterHead.cpp" />
    <ClCompile Include="Source\Object\Monster\CShooterMonster.cpp" />
    <ClCompile Include="Source\Object\Trigger\CTrigger.cpp" />
    <ClCompile Include="Source\Object\UI\ChatUI\CTextUI.cpp" />
    <ClCompile Include="Source\CHook.cpp" />
    <ClCompile Include="Source\Component\Animator\Animation\CAnimation.cpp" />
    <ClCompile Include="Source\Component\Animator\CAnimator.cpp" />
    <ClCompile Include="Source\Component\Collider\CCollider.cpp" />
    <ClCompile Include="Source\Component\Gravity\CGravity.cpp" />
    <ClCompile Include="Source\Component\RigidBody\CRigidBody.cpp" />
    <ClCompile Include="Source\Core\CCore.cpp" />
    <ClCompile Include="Source\Manager\BrushMgr\CBrushManager.cpp" />
    <ClCompile Include="Source\Header\func.cpp" />
    <ClCompile Include="Source\Manager\Camera\CCamera.cpp" />
    <ClCompile Include="Source\Manager\CollisionMgr\CCollisionMgr.cpp" />
    <ClCompile Include="Source\Manager\CSoundMgr\CSoundMgr.cpp" />
    <ClCompile Include="Source\Manager\EventMgr\CEventMgr.cpp" />
    <ClCompile Include="Source\Manager\KeyMgr\CKeyMgr.cpp" />
    <ClCompile Include="Source\Manager\PathMgr\CPathMgr.cpp" />
    <ClCompile Include="Source\Manager\ResMgr\CResMgr.cpp" />
    <ClCompile Include="Source\Manager\SceneMgr\CSceneMgr.cpp" />
    <ClCompile Include="Source\Manager\TimeMgr\CTimeMgr.cpp" />
    <ClCompile Include="Source\Manager\UIMgr\CUIMgr.cpp" />
    <ClCompile Include="Source\Module\AI\AI.cpp" />
    <ClCompile Include="Source\Module\AI\State\CState.cpp" />
    <ClCompile Include="Source\Module\AI\State\Idle\CIdleState.cpp" />
    <ClCompile Include="Source\Module\AI\State\Trace\CTraceState.cpp" />
    
    <ClCompile Include="Source\Object\CMonster.cpp" />
    <ClCompile Include="Source\Object\GameObject.cpp" />
    <ClCompile Include="Source\Object\Ground\CGround.cpp" />
    <ClCompile Include="Source\Object\Ground\CNormalGround.cpp" />
    <ClCompile Include="Source\Object\Tile\CBackGround.cpp" />
    <ClCompile Include="Source\Object\Tile\CTile.cpp" />
    <ClCompile Include="Source\Object\UI\BtnUI\CBtnUI.cpp" />
    <ClCompile Include="Source\Object\UI\CUI.cpp" />
    <ClCompile Include="Source\Object\UI\DamageEffect\CDamageEffectUI.cpp" />
    <ClCompile Include="Source\Object\UI\PanelUI\CPanelUI.cpp" />
    <ClCompile Include="Source\Prefab\CGroundPrefab.cpp" />
    <ClCompile Include="Source\Prefab\CMonPrefab.cpp" />
    <ClCompile Include="Source\Resource\CRes.cpp" />
    <ClCompile Include="Source\Resource\Sound\CSound.cpp" />
    <ClCompile Include="Source\Resource\Texture\CTexture.cpp" />
    <ClCompile Include="Source\Scene\CScene.cpp" />
    <ClCompile Include="Source\Scene\Scene_Stage_01\Scene_Stage_01.cpp" />
    <ClCompile Include="Source\Scene\Scene_Title\CScene_Title.cpp" />
    <ClCompile Include="Source\Scene\Scene_Tool\CScene_Tool.cpp" />
    <ClCompile Include="Source\Tool\CTool.cpp" />
    <ClCompile Include="Source\Tool\Vec2.cpp" />
    <ClCompile Include="Source\Main.cpp" />
    <ClCompile Include="Source\MouseCursor.cpp" />
    <ClCompile Include="Source\PlayerArm.cpp" />
    <ClCompile Include="Source\Raycast.cpp" />
    <ClCompile Include="Source\SPlayer.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="WinAPI_Proj.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="small.ico" />
    <Image Include="WinAPI_Proj.ico" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>